#!/usr/bin/env python
# encoding:utf-8
from . import *
import requests
import json
from flask import Response as FlaskResponse, stream_template

# 外部聊天服务的地址
CHAT_SERVICE_URL = "http://127.0.0.1:28123/chat"


@r.route("/chat", methods=['POST'])
def chat():
    """
    SOAR聊天接口 - 封装外部聊天服务
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 聊天请求参数
        schema:
          type: object
          required:
            - message
          properties:
            message:
              type: string
              description: 用户消息
            conversation_id:
              type: string
              description: 对话ID（可选，用于维护对话上下文）
    responses:
      200:
        description: 聊天响应（流式）
        content:
          text/event-stream:
            schema:
              type: string
              description: Server-Sent Events格式的流式响应
      400:
        description: 参数错误
      401:
        description: 认证失败
      500:
        description: 服务器错误
    """
    try:
        # 获取请求参数
        message = request.json.get("message", "")
        conversation_id = request.json.get("conversation_id", None)
        
        if not message.strip():
            return Response.re(err={"code": 400, "message": "消息内容不能为空"})
        
        # 获取当前用户ID（用于日志记录）
        token = request.headers.get('token', '')
        user_id = redis.get(token) if redis else None
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 构建发送给外部服务的请求数据
        payload = {
            "message": message,
            "conversation_id": conversation_id
        }
        
        # 记录请求日志
        logger.info(f"[SOAR Chat] 用户 {user_id} 发起聊天请求: {message[:50]}...")
        
        def generate_stream():
            """生成流式响应"""
            try:
                # 向外部聊天服务发送请求
                with requests.post(CHAT_SERVICE_URL, json=payload, stream=True, timeout=300) as response:
                    if response.status_code != 200:
                        error_msg = f"外部聊天服务返回错误: {response.status_code}"
                        logger.error(f"[SOAR Chat] {error_msg}")
                        yield f"data: {json.dumps({'type': 'error', 'content': error_msg}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 转发流式响应
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith('data:'):
                                # 直接转发SSE数据
                                yield f"{decoded_line}\n\n"
                            
            except requests.exceptions.Timeout:
                error_msg = "聊天服务响应超时"
                logger.error(f"[SOAR Chat] {error_msg}")
                yield f"data: {json.dumps({'type': 'error', 'content': error_msg}, ensure_ascii=False)}\n\n"
            except requests.exceptions.ConnectionError:
                error_msg = "无法连接到聊天服务"
                logger.error(f"[SOAR Chat] {error_msg}")
                yield f"data: {json.dumps({'type': 'error', 'content': error_msg}, ensure_ascii=False)}\n\n"
            except Exception as e:
                error_msg = f"聊天服务异常: {str(e)}"
                logger.error(f"[SOAR Chat] {error_msg}")
                yield f"data: {json.dumps({'type': 'error', 'content': error_msg}, ensure_ascii=False)}\n\n"
        
        # 返回流式响应
        return FlaskResponse(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            }
        )
        
    except Exception as e:
        logger.error(f"[SOAR Chat] 聊天接口异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/chat/status", methods=['GET'])
def chat_status():
    """
    检查聊天服务状态
    ---
    responses:
      200:
        description: 服务状态正常
      503:
        description: 聊天服务不可用
    """
    try:
        # 检查外部聊天服务是否可用
        response = requests.get(f"{CHAT_SERVICE_URL.replace('/chat', '/health')}", timeout=5)
        if response.status_code == 200:
            return Response.re(data={"status": "available", "message": "聊天服务正常"})
        else:
            return Response.re(err={"code": 503, "message": "聊天服务不可用"})
    except requests.exceptions.RequestException:
        # 如果健康检查接口不存在，尝试简单的连接测试
        try:
            response = requests.post(CHAT_SERVICE_URL, 
                                   json={"message": "test", "conversation_id": None}, 
                                   timeout=5)
            return Response.re(data={"status": "available", "message": "聊天服务正常"})
        except requests.exceptions.RequestException:
            return Response.re(err={"code": 503, "message": "聊天服务不可用"})
    except Exception as e:
        logger.error(f"[SOAR Chat] 状态检查异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/chat/config", methods=['GET'])
def chat_config():
    """
    获取聊天配置信息
    ---
    responses:
      200:
        description: 配置信息
    """
    try:
        # 获取当前用户ID
        token = request.headers.get('token', '')
        user_id = redis.get(token) if redis else None
        if not user_id:
            return Response.re(err=ErrToken)
        
        config = {
            "service_url": CHAT_SERVICE_URL,
            "features": {
                "streaming": True,
                "conversation_context": True,
                "tool_calling": True
            },
            "limits": {
                "max_message_length": 4000,
                "timeout_seconds": 300
            }
        }
        
        return Response.re(data=config)
        
    except Exception as e:
        logger.error(f"[SOAR Chat] 配置获取异常: {str(e)}")
        return Response.re(err=Err)
