-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: w5_db
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.22.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `w5_notification`
--

DROP TABLE IF EXISTS `w5_notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_notification` (
  `notification_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `source` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `notification_type` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('read','unread') COLLATE utf8mb4_general_ci DEFAULT 'unread',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `result_data` json DEFAULT NULL,
  `execution_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`notification_id`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `w5_notification`
--

LOCK TABLES `w5_notification` WRITE;
/*!40000 ALTER TABLE `w5_notification` DISABLE KEYS */;
INSERT INTO `w5_notification` VALUES ('16926d2197c2fc7f550c650e25a7db02','b\'1\'','2025/08/01 18:10发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:10:15','2025-08-01 18:10:15','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:07\", \"hazard_level\": \"中危\", \"repeat_count\": 215}',''),('1bcbc85c017b758f5d8361f9af56bd25','b\'1\'','2025/08/01 19:19发现【S60000系统】存在待处理数事件','S60000系统','待处理数','unread','2025-08-01 19:19:12','2025-08-01 19:19:12','{\"_type\": \"工作联系单\", \"state\": \"已反馈\", \"title\": \"武汉公司外网终端存在访问恶意域名20250707-02\", \"end_time\": \"2025-07-07 18:14:00\", \"feedback\": \"\", \"dealstate\": \"\", \"file_path\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\s60000_2.0\\\\download\\\\武汉公司外网终端存在访问恶意域名20250707-02.zip\", \"info_code\": \"LX-1520250707-002\", \"vulnlevel\": \"\", \"detail_title\": \"武汉公司外网终端存在访问恶意域名20250707-02\", \"publish_time\": \"2025-07-07 16:14:40\", \"detail_author\": \"\", \"detail_context\": \"武汉公司，省网监中心在网络安全监控中发现计算机终端(IP地址************)存在访问恶意IP(***************:447)的情况，安全设备告警为终端“失陷”，经查该计算机终端为你单位所属，详见附件1.为防止恶意攻击行为传播，省级网监中心已在互联网大区终端区出口封禁IP地址限制其访问互联网恶意IP(***************:447)。2.请贵单位立即对IP地址为************的终端进行断网隔离，并排查该终端是否感染病毒若该终端存在感染情况，请排查病毒是否横向传播至其他设备。3.请两小时内通过S6000反馈终端************是否感染病毒排查结果，以及病毒是否横向传播初步排查结果。4.请48小时内通过邮件反馈附件2《恶意程序-XX公司-反馈单》，邮箱为：<EMAIL>.cn5.若确认该终端(IP地址************)未感染病毒，请通过S6000反馈该情况，省网监中心在收到反馈后将第一时间对该IP地址进行解封。6.若无法排查出具体终端感染原因，建议对该终端进行格式化并重装操作系统，重装前做好数据务份。7.30天内因整改不彻底导致重复出现恶意告警，或未按上述要求反馈，将通报处理。\", \"feedbackendtime\": \"\", \"detail_vulnlevel\": \"\", \"detail_recordtime\": \"\", \"toBeCorrectedTime\": \"\", \"detail_create_time\": \"2025-07-07 16:14:40\", \"detail_vulnproduct\": \"\", \"detail_Handleadvice\": \"\", \"detail_contactnumber\": \"\", \"detail_feedbackendtime\": \"\", \"detail_feedbackrequire\": \"\"}',''),('1e2b9ef4e4f185897e86dafad0683858','b\'1\'','2025/08/01 18:10发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:10:15','2025-08-01 18:10:15','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:08\", \"hazard_level\": \"中危\", \"repeat_count\": 216}',''),('22ba8ea206ca0f841052fee4b43a5bf4','b\'1\'','2025/08/01 19:19发现【S60000系统】存在待处理数事件','S60000系统','待处理数','unread','2025-08-01 19:19:12','2025-08-01 19:19:12','{\"_type\": \"工作联系单\", \"state\": \"已反馈\", \"title\": \"武汉公司内网终端存在访问恶意域名20250716-01\", \"end_time\": \"2025-07-17 10:00:00\", \"feedback\": \"\", \"dealstate\": \"\", \"file_path\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\s60000_2.0\\\\download\\\\武汉公司内网终端存在访问恶意域名20250716-01.zip\", \"info_code\": \"LX-1520250716-004\", \"vulnlevel\": \"\", \"detail_title\": \"武汉公司内网终端存在访问恶意域名20250716-01\", \"publish_time\": \"2025-07-16 16:55:42\", \"detail_author\": \"\", \"detail_context\": \"武汉公司，省网监中心在网络安全监控中发现计算机终端（IP地址***********）存在访问恶意域名（xred.mooo.com）的情况，安全设备告警为设备“失陷”，经查该计算机终端为你单位所属，详见附件1.请贵单位立即对IP地址为***********的终端进行断网隔离，并排查该终端是否感染病毒；若该终端存在感染情况，请排查病毒是否横向传播至其他设备。2.请两小时内通过S6000反馈终端***********是否感染病毒排查结果，以及病毒是否横向传播初步排查结果。3.48小时内通过邮件反馈附件2《恶意程序-XX公司-反馈单》，邮箱为：<EMAIL>。4.若无法排查出具体终端感染原因，建议对该终端进行格式化并重装操作系统，重装前做好数据备份。5.30天内因整改不彻底导致重复出现恶意告警，或未按上述要求反馈，将通报处理。\", \"feedbackendtime\": \"\", \"detail_vulnlevel\": \"\", \"detail_recordtime\": \"\", \"toBeCorrectedTime\": \"\", \"detail_create_time\": \"2025-07-16 16:55:42\", \"detail_vulnproduct\": \"\", \"detail_Handleadvice\": \"\", \"detail_contactnumber\": \"\", \"detail_feedbackendtime\": \"\", \"detail_feedbackrequire\": \"\"}',''),('260776ff7d88562ff995a8f897658b8e','b\'1\'','2025/08/01 18:04发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:04:44','2025-08-01 18:04:44','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:03\", \"hazard_level\": \"中危\", \"repeat_count\": 215}',''),('36720f756009f7740a68d375c57cea70','b\'1\'','2025/08/01 18:18发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:18:13','2025-08-01 18:18:13','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:17\", \"hazard_level\": \"中危\", \"repeat_count\": 217}',''),('52cd0bd5bd5e4a46949b18da4c0a5218','b\'1\'','2025/08/01 18:10发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:10:15','2025-08-01 18:10:15','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:07\", \"hazard_level\": \"中危\", \"repeat_count\": 214}',''),('530fd71753c17cb026edc34f5f6a82ec','b\'1\'','2025/08/01 18:04发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:04:44','2025-08-01 18:04:44','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:02\", \"hazard_level\": \"中危\", \"repeat_count\": 214}',''),('6508ebc2072a9a19ae50cd5f177828a2','b\'1\'','2025/08/01 18:29发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:29:47','2025-08-01 18:29:47','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:28\", \"hazard_level\": \"中危\", \"repeat_count\": 220}',''),('73a94e9b63814904665a856e9361f14b','b\'1\'','2025/08/01 17:58发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 17:58:01','2025-08-01 17:58:02','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日17:57\", \"hazard_level\": \"中危\", \"repeat_count\": 212}',''),('8c352a155d6b31c3bd7456772b8d1666','b\'1\'','2025/08/01 16:43发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 16:43:36','2025-08-01 16:43:36','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日16:42\", \"hazard_level\": \"中危\", \"repeat_count\": 198}',''),('939d339b46646c7aa51b904daedeb98e','b\'1\'','2025/08/01 17:58发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 17:58:01','2025-08-01 17:58:02','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日17:53\", \"hazard_level\": \"中危\", \"repeat_count\": 213}',''),('b71ccd7c9e57ebd1283c95ee6a79d452','b\'1\'','2025/08/01 17:58发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 17:58:01','2025-08-01 17:58:02','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日17:57\", \"hazard_level\": \"中危\", \"repeat_count\": 213}',''),('d2fe214a9515573c942fa218c14a4e6b','b\'1\'','2025/08/01 16:43发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 16:43:36','2025-08-01 16:43:36','{\"alarm_sip\": \"***********\", \"rule_name\": \"发现存在弱口令风险(REDIS)\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网络攻击\", \"access_time\": \"8月1日16:43\", \"hazard_level\": \"高危\", \"repeat_count\": 76}',''),('d7dbe8e5bb976ed2853e987c72dc5f4a','b\'1\'','2025/08/01 16:43发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 16:43:36','2025-08-01 16:43:36','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日16:43\", \"hazard_level\": \"中危\", \"repeat_count\": 199}',''),('dc3662f4dd661ad1b67662721484e5ef','b\'1\'','2025/08/01 18:04发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:04:44','2025-08-01 18:04:44','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:02\", \"hazard_level\": \"中危\", \"repeat_count\": 213}',''),('dca13a39439fab93879fb97c04cad6ff','b\'1\'','2025/08/01 18:29发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:29:47','2025-08-01 18:29:47','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:27\", \"hazard_level\": \"中危\", \"repeat_count\": 218}',''),('eafa5e102d95d19ff096e9d9f52f1767','b\'1\'','2025/08/01 18:18发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:18:13','2025-08-01 18:18:13','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:17\", \"hazard_level\": \"中危\", \"repeat_count\": 216}',''),('f4ca22b90e8b9239d063973e46543911','b\'1\'','2025/08/01 16:43发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','read','2025-08-01 16:43:36','2025-08-01 16:54:45','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日16:42\", \"hazard_level\": \"中危\", \"repeat_count\": 197}',''),('fe5911e23c18f980c7395d170883c008','b\'1\'','2025/08/01 18:29发现【奇安信天眼】存在危急告警事件','奇安信天眼','危急告警','unread','2025-08-01 18:29:47','2025-08-01 18:29:47','{\"alarm_sip\": \"**************\", \"rule_name\": \"Spring Boot框架敏感信息泄露漏洞\", \"attack_sip\": \"***********\", \"host_state\": \"成功\", \"table_name\": \"网页漏洞利用\", \"access_time\": \"8月1日18:27\", \"hazard_level\": \"中危\", \"repeat_count\": 219}','');
/*!40000 ALTER TABLE `w5_notification` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-01 19:20:53
