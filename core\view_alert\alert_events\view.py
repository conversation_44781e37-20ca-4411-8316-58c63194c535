from . import * 
BASE_URL = 'https://**************:443/'
#BASE_URL = 'https://*************:12601/'
MESSAGE_ENDPOINT = 'rest/rules/es/'
CONTENT = 'alerts_tail/'
COUNT = 'alerts_count/'
SOURCE_ENDPOINT = '/rules/source/add_public'
TOKEN = '4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Referer': 'https://*************:12601/evebox/',
    'Sec-<PERSON>tch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Content-Type' : 'application/json',
    #'Authorization' : 'Token 4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
}

COOKIE = {
    'csrftoken': 'zODMsJeewZmlLFLGHUiOaAukvTPKGd1PTjyryZr5fJhWA8ChediezKF7wTJCo7H5',
    'sessionid': 'chgx7lxye8uxc8jew0xpir7gjb2iv4wq'
}

@r.route("/events/count", methods=['GET'])
def get_alert_message_count():
    time_unit = request.args.get('time')
    number = request.args.get('number')
    
    # 初始化响应字典（修正语法：补充逗号，使用字典访问方式）
    count_data = {
        "code": "",
        # "pre_alert_count": data.get("prev_doc_count"),
        "data": "",
        "msg": ""
    }

    # 参数校验
    if not time_unit or not number:
        count_data["code"] = "400"  # HTTP 400表示客户端请求错误
        count_data["msg"] = "参数缺失：必须提供time和number参数"
        return count_data, 400  # 明确返回HTTP状态码

    # 计算时间范围
    try:
        if time_unit == 'day':
            delta = timedelta(days=int(number))
        elif time_unit == 'week':
            delta = timedelta(weeks=int(number))
        elif time_unit == 'month':
            delta = timedelta(days=int(number) * 30)  # 简化月份计算
        else:
            count_data["code"] = "400"
            count_data["msg"] = f"无效的时间单位：{time_unit}"
            return count_data, 400
    except ValueError:
        count_data["code"] = "400"
        count_data["msg"] = "参数number必须是整数"
        return count_data, 400

    # 生成时间戳
    from_time = datetime.utcnow() - delta
    from_timestamp_ms = int(from_time.timestamp() * 1000)
    to_timestamp_ms = int(datetime.utcnow().replace(tzinfo=timezone.utc).timestamp() * 1000)

    # 构建请求URL
    url = f'{BASE_URL}{MESSAGE_ENDPOINT}{COUNT}?prev=1&from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'
    
    # 发起外部请求
    try:
        response = requests.get(
            url,
            headers=HEADERS,
            cookies=COOKIE,
            verify=False,
            timeout=10  # 添加超时设置避免阻塞
        )

        responseJson = response.json()
        count = {
            "alert_count" : responseJson.get("doc_count")
        }
        
        if response.status_code == 200:
            count_data["code"] = "200"
            count_data["data"] = count
            count_data["msg"] = "success"
            return count_data
        else:
            # 处理非200状态码
            count_data["code"] = str(response.status_code)
            count_data["msg"] = f"外部接口错误：{response.text[:100]}"  # 截断过长错误信息
            return count_data, 502  # 502表示网关错误
    except requests.Timeout:
        count_data["code"] = "504"
        count_data["msg"] = "请求外部接口超时"
        return count_data, 504
    except requests.RequestException as e:
        count_data["code"] = "500"
        count_data["msg"] = f"请求失败：{str(e)}"
        return count_data, 500
    except Exception as e:
        print(response.text)
        count_data["code"] = "500"
        count_data["msg"] = f"服务器内部错误：{str(e)}"
        return count_data, 500


@r.route("/events/soar/get", methods = ['GET'])
def get_alert_events_soar():
    time_ = request.args.get('time')
    message = {
        "code": "",
        "data" : {},
        "msg" : ""
    }

    if not time_unit or not number:
        count_data["code"] = "400"  # HTTP 400表示客户端请求错误
        count_data["msg"] = "参数缺失：必须提供time和number参数"
        return count_data, 400  # 明确返回HTTP状态码
    
    delta = timedelta(hours = 1)
    if time_ == 'hour':
        delta = timedelta(hours = 1)
    elif time_ == 'day':
        delta = timedelta(days = 1)
    elif time_ == 'week':
        delta = timedelta(weeks = 1)
    elif time_ == 'month':
        delta = timedelta(days = 30)

    from_time = datetime.utcnow() - delta
    from_timestamp_ms = int(from_time.timestamp() * 1000)
    to_timestamp_ms = int(datetime.utcnow().timestamp() * 1000)

    url = f'{BASE_URL}{MESSAGE_ENDPOINT}{CONTENT}?from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'
    print(url)
    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE,verify=False)
        data = response.json()
        extracted_results = []
        id = 1
        for result in data.get("results", []):
            unit = {
                "event_id" : id,
                "event_type" : result.get("event_type"),
                "severity" : result.get("alert", {}).get("metadata", {}).get("confidence", []),
                "detected_at" : result.get("flow", {}).get("start"),
                "detail" : {
                    "src_ip": result.get("src_ip"),
                    "src_port": result.get("src_port"),
                    "dest_ip": result.get("dest_ip"),
                    "dest_port": result.get("dest_port")
                }
            }
            id += 1
            extracted_results.append(unit)
        return jsonify({"results": extracted_results}) 
    except Exception as err:
        return {"err": str(err)}

@r.route("/events/list", methods = ['GET'])
def get_alert_message_list():
    time_unit = request.args.get('time')
    number = request.args.get('number')

    message = {
        "code": "",
        "data" :{},
        "msg" : ""
    }

    if not time_unit or not number:
        count_data["code"] = "400"  # HTTP 400表示客户端请求错误
        count_data["msg"] = "参数缺失：必须提供time和number参数"
        return count_data, 400  # 明确返回HTTP状态码

    if time_unit == 'day':
        delta = timedelta(days = int(number))
    elif time_unit == 'week':
        delta = timedelta(weeks = int(number))
    elif time_unit == 'month':
        delta = timedelta(days = int(number) * 30)

    from_time = datetime.utcnow() - delta
    from_timestamp_ms = int(from_time.timestamp() * 1000)
    to_timestamp_ms = int(datetime.utcnow().replace(tzinfo=timezone.utc).timestamp() * 1000)

    url = f'{BASE_URL}{MESSAGE_ENDPOINT}{CONTENT}?from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'
    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE,verify=False)
        data = response.json()
        extracted_results = []
        for result in data.get("results", []):
            extracted_result = {
                "timestamp": result.get("@timestamp"),
                "alert": {
                    "action": result.get("alert", {}).get("action"),
                    "category": result.get("alert", {}).get("category"),
                    "metadata": {
                        "confidence": result.get("alert", {}).get("metadata", {}).get("confidence", []),
                        "created_at": result.get("alert", {}).get("metadata", {}).get("created_at", []),
                        "signature_severity": result.get("alert", {}).get("metadata", {}).get("signature_severity", [])
                    },
                    "rev": result.get("alert", {}).get("rev"),
                    "severity": result.get("alert", {}).get("severity"),
                    "signature": result.get("alert", {}).get("signature"),
                    "signature_id": result.get("alert", {}).get("signature_id")
                },
                "app_proto": result.get("app_proto"),
                "capture_file": result.get("capture_file"),
                "dest_ip": result.get("dest_ip"),
                "dest_port": result.get("dest_port"),
                "direction": result.get("direction"),
                "ether": {
                    "dest_mac": result.get("ether", {}).get("dest_mac"),
                    "src_mac": result.get("ether", {}).get("src_mac")
                },
                "event_type": result.get("event_type"),
                "flow": {
                    "bytes_toclient": result.get("flow", {}).get("bytes_toclient"),
                    "bytes_toserver": result.get("flow", {}).get("bytes_toserver"),
                    "dest_ip": result.get("flow", {}).get("dest_ip"),
                    "dest_port": result.get("flow", {}).get("dest_port"),
                    "pkts_toclient": result.get("flow", {}).get("pkts_toclient"),
                    "pkts_toserver": result.get("flow", {}).get("pkts_toserver"),
                    "src_ip": result.get("flow", {}).get("src_ip"),
                    "src_port": result.get("flow", {}).get("src_port"),
                    "start": result.get("flow", {}).get("start")
                },
                "flow_id": result.get("flow_id"),
                "geoip": {
                    "continent_code": result.get("geoip", {}).get("continent_code"),
                    "country_code2": result.get("geoip", {}).get("country_code2"),
                    "country_code3": result.get("geoip", {}).get("country_code3"),
                    "country_name": result.get("geoip", {}).get("country_name"),
                    "ip": result.get("geoip", {}).get("ip"),
                    "latitude": result.get("geoip", {}).get("latitude"),
                    "location": result.get("geoip", {}).get("location"),
                    "longitude": result.get("geoip", {}).get("longitude"),
                    "timezone": result.get("geoip", {}).get("timezone")
                },
                "in_iface": result.get("in_iface"),
                "proto": result.get("proto"),
                "src_ip": result.get("src_ip"),
                "src_port": result.get("src_port"),
                "stream": result.get("stream"),
                "tags": result.get("tags"),
                "type": result.get("type")
            }
            extracted_results.append(extracted_result)

        # 构建新的JSON结构
        message['code'] = str(response.status_code)
        message['data'] = extracted_results
        message['msg'] = "success"
        return message
    except Exception as e:
        message['msg'] = str(e)
        message['code'] = str(response.status_code)
        return message


@r.route("/events/detect", methods=['GET'])
def get_events_detect():
    url = f"{BASE_URL}evebox/api/1/alerts?tags=-evebox.archived"
    start_time_str = request.args.get('start_time')
    end_time_str = request.args.get('end_time')
    src_ip = request.args.get('src_ip')  # IP过滤参数
    dest_ip = request.args.get('dest_ip')
    proto = request.args.get('proto')  # 协议过滤参数
    encode = request.args.get('encode')
    export = request.args.get('export')  # 新增：是否导出为csv

    # 分页参数
    try:
        pagesize = int(request.args.get('pagesize', 10))
        page = int(request.args.get('page', 1))
        if pagesize <= 0 or page <= 0:
            raise ValueError("pagesize and page must be positive integers")
    except ValueError as e:
        return {
            "code": "400",
            "data": {},
            "msg": f"Invalid pagination parameters: {e}"
        }

    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False)
        original_data = response.json()
        filtered_items = []
        count = 1

        for event in original_data.get("events", []):
            event_id = event.get("_id")
            if not event_id:
                continue
            detect_time_raw = event.get("_source", {}).get("timestamp")
            detect_datetime = None
            detect_time_str_fmt = ""

            if detect_time_raw:
                detect_datetime = datetime.strptime(detect_time_raw, "%Y-%m-%dT%H:%M:%S.%f%z")
                detect_time_str_fmt = detect_datetime.strftime("%Y-%m-%d %H:%M:%S")

            if start_time_str:
                start_time = datetime.strptime(start_time_str, "%Y-%m-%d")
                if detect_datetime and detect_datetime.replace(tzinfo=None) < start_time:
                    continue
            if end_time_str:
                end_time = datetime.strptime(end_time_str, "%Y-%m-%d")
                if detect_datetime and detect_datetime.replace(tzinfo=None) > end_time:
                    continue

            item = {
                "dest_ip": event.get("_source", {}).get("dest_ip"),
                "detect_time": detect_time_str_fmt,
                "id": str(count),
                "proto": event.get("_source", {}).get("app_proto"),
                "src_ip": event.get("_source", {}).get("src_ip"),
                "encode": "",
                "detail": ""
            }

            # 获取详情
            detail_url = f'{BASE_URL}evebox/api/1/event/{event_id}'
            try:
                detail_response = requests.get(detail_url, headers=HEADERS, cookies=COOKIE, verify=False)
                if detail_response.status_code == 200:
                    detail_json = detail_response.json()
                    item["detail"] = detail_json
                    item["encode"] = detail_json.get("_id")
                else:
                    item["detail"] = {"error": f"HTTP {detail_response.status_code}"}
            except Exception as e:
                item["detail"] = {"error": str(e)}

            # 筛选
            srcip_match = (not src_ip) or (item['src_ip'] == src_ip)
            destip_match = (not dest_ip) or (item['dest_ip'] == dest_ip)
            proto_match = (not proto) or (item['proto'] == proto)
            encode_match = (not encode) or (item['encode'] == encode)

            if srcip_match and destip_match and proto_match and encode_match:
                filtered_items.append(item)
                count += 1

        # 分页
        total = len(filtered_items)
        start_index = (page - 1) * pagesize
        end_index = start_index + pagesize
        paged_items = filtered_items[start_index:end_index]

        # 如果导出为 CSV，包含每个条目的 detail 展开字段
        if export == "csv":
            def flatten(d, parent_key='', sep='.'):
                """
                递归展开嵌套字典为扁平结构，键名使用 a.b.c 格式
                """
                items = []
                for k, v in d.items():
                    new_key = f"{parent_key}{sep}{k}" if parent_key else k
                    if isinstance(v, dict):
                        items.extend(flatten(v, new_key, sep=sep).items())
                    elif isinstance(v, list):
                        # 列表使用 JSON 字符串保存
                        items.append((new_key, json.dumps(v, ensure_ascii=False)))
                    else:
                        items.append((new_key, v))
                return dict(items)

            # 提取所有字段名（统一字段）
            fieldnames_set = set()
            rows = []
            for row in paged_items:
                base = {
                    "id": row["id"],
                    "src_ip": row["src_ip"],
                    "dest_ip": row["dest_ip"],
                    "proto": row["proto"],
                    "detect_time": row["detect_time"],
                    "encode": row["encode"]
                }
                detail_flat = flatten(row["detail"])
                full_row = {**base, **detail_flat}
                fieldnames_set.update(full_row.keys())
                rows.append(full_row)

            fieldnames = sorted(list(fieldnames_set))  # 排序字段名保持一致

            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            for row in rows:
                writer.writerow(row)

            csv_data = output.getvalue()
            output.close()

            return Response(
                csv_data,
                mimetype="text/csv",
                headers={
                    "Content-disposition": "attachment; filename=detect_events_full.csv"
                }
            )

        # 默认返回 JSON
        return {
            "code": str(response.status_code),
            "data": {
                "total": total,
                "pagesize": pagesize,
                "page": page,
                "items": paged_items
            },
            "msg": "success"
        }

    except Exception as err:
        return {
            "code": "500",
            "data": {},
            "msg": str(err)
        }

def get_selks_info(start_time_str=None, end_time_str=None):
    url = f"{BASE_URL}evebox/api/1/alerts?tags=-evebox.archived"

    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False)
        original_data = response.json()
        filtered_items = []

        for event in original_data.get("events", []):
            event_id = event.get("_id")
            if not event_id:
                continue

            # 处理时间
            detect_time = event.get("_source", {}).get("timestamp")
            if not detect_time:
                continue

            # 解析事件时间（带时区）
            detect_datetime = datetime.strptime(detect_time, "%Y-%m-%dT%H:%M:%S.%f%z")
            
            # 时间范围过滤
            if start_time_str:
                start_time = datetime.strptime(start_time_str, "%Y-%m-%d")
                if detect_datetime.replace(tzinfo=None) < start_time:
                    continue
            
            if end_time_str:
                end_time = datetime.strptime(end_time_str, "%Y-%m-%d")
                if detect_datetime.replace(tzinfo=None) > end_time:
                    continue

            # 构建事件项
            item = {
                "dest_ip": event.get("_source", {}).get("dest_ip"),
                "detect_time": detect_datetime.strftime("%Y-%m-%d"),  # 格式化输出
                "src_ip": event.get("_source", {}).get("src_ip"),
                "dst_port": "",
                "src_port": "",
                "事件类型": "",
                "级别": "",
                "事件描述": "",
                "detail": "",
            }

            # 获取详情
            detail_url = f'{BASE_URL}evebox/api/1/event/{event_id}'
            try:
                detail_response = requests.get(detail_url, headers=HEADERS, cookies=COOKIE, verify=False)
                if detail_response.status_code == 200:
                    detail_json = detail_response.json()
                    item.update({
                        '事件类型': detail_json.get("_source", {}).get("alert", {}).get("category"),
                        '级别': detail_json.get("_source", {}).get("alert", {}).get("metadata", {}).get("confidence", []),
                        '事件描述': detail_json.get("_source", {}).get("alert", {}).get("signature"),
                        'dst_port': detail_json.get("_source", {}).get("dest_port"),
                        'src_port': detail_json.get("_source", {}).get("flow", {}).get("src_port"),
                        'detail' : detail_json
                    })
            except Exception as e:
                item["事件描述"] = f"Detail fetch error: {str(e)}"

            filtered_items.append(item)

        return {
            "code": "200",
            "data": filtered_items,
            "msg": "success"
        }

    except Exception as err:
        return {
            "code": "500",
            "data": {},
            "msg": str(err)
        }

# @r.route("/events/detect1", methods = ['GET'])
# def get_events_detect_():
#     url = "https://*************:12601/evebox/api/1/alerts?tags=-evebox.archived"
#     start_time_str = request.args.get('start_time')
#     end_time_str = request.args.get('end_time')
#     ip = request.args.get('ip')  # 新增IP过滤参数
#     proto = request.args.get('proto')
#     try:
#         response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False)
#         original_data = response.json()
#         items = []
#         count = 1
#         for event in original_data.get("events", []):
#             item = {
#                 "dest_ip": event.get("_source", {}).get("dest_ip"),
#                 "detect_time": event.get("_source", {}).get("timestamp"),
#                 "id": str(count),
#                 "proto": event.get("_source", {}).get("app_proto"),
#                 "src_ip": event.get("_source", {}).get("src_ip"),
#                 "encode": "",
#                 "detail": ""
#             }
#             event_id = event.get("_id")
#             if not event_id:
#                 continue

#             detail_url = f'https://*************:12601/evebox/api/1/event/{event_id}'
#             try:
#                 detail_response = requests.get(detail_url, headers=HEADERS, cookies=COOKIE, verify=False)
#                 if detail_response.status_code == 200:
#                     detail_json = detail_response.json()
#                     item["detail"] = detail_json
#                     item["encode"] = detail_json.get("_id")
#                 else:
#                     item["detail"] = {"error": f"HTTP {detail_response.status_code}"}
#             except Exception as e:
#                 item["detail"] = {"error": str(e)}  # fix this line

#             # 初始化默认匹配状态
#             ip_match = True
#             if ip:
#                 ip_match = (item['src_ip'] == ip or item['dest_ip'] == ip)

#             proto_match = True
#             if proto:
#                 proto_match = (item['proto'] == proto)

#             if ip_match and proto_match:
#                 items.append(item)
#                 count += 1

#         return {
#             "code": str(response.status_code),
#             "data": items,
#             "msg": "success"
#         }

#     except Exception as err:
#         return {
#             "code": "500",  # 默认值避免 response 未定义
#             "data": {},
#             "msg": str(err)
#         }


@r.route("/events/number/day/list", methods = ['GET'])
def get_events_number_day_list():
    day_number = request.args.get('day_number', default = '7')
    response_data = {
        "code": "200",
        "data": [],
        "msg": "success"
    }

    # 计算过去7天的日期范围
    today = datetime.utcnow().date()
    date_list = [today - timedelta(days=i) for i in range(int(day_number))]  # 过去7天（不包括今天）

    try:
        daily_counts = []
        for date in date_list:
            # 计算当天的起止时间（UTC）
            start_time = datetime.combine(date, datetime.min.time())
            end_time = datetime.combine(date, datetime.max.time())

            # 转换为时间戳（毫秒）
            from_timestamp_ms = int(start_time.timestamp() * 1000)
            to_timestamp_ms = int(end_time.timestamp() * 1000)

            # 构建请求URL
            url = f'{BASE_URL}{MESSAGE_ENDPOINT}{COUNT}?prev=1&from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'

            # 发起请求
            response = requests.get(
                url,
                headers=HEADERS,
                cookies=COOKIE,
                verify=False,
                timeout=10
            )

            if response.status_code != 200:
                raise Exception(f"外部接口错误：{response.text[:100]}")

            # 解析响应
            response_json = response.json()
            daily_counts.append({
                "date": date.strftime("%Y-%m-%d"),  # 格式化日期
                "count": response_json.get("doc_count", 0)  # 默认0，避免KeyError
            })

        response_data["data"] = daily_counts
        return response_data

    except requests.Timeout:
        response_data.update({
            "code": "504",
            "msg": "请求外部接口超时"
        })
        return response_data, 504
    except requests.RequestException as e:
        response_data.update({
            "code": "500",
            "msg": f"请求失败：{str(e)}"
        })
        return response_data, 500
    except Exception as e:
        response_data.update({
            "code": "500",
            "msg": f"服务器内部错误：{str(e)}"
        })
        return response_data, 500

@r.route("/events/number/month/list", methods = ['GET'])
def get_events_number_month_list():
    # 获取最近几个月的数量，默认6个月
    month_number = int(request.args.get('month_number', default='6'))
    
    response_data = {
        "code": "200",
        "data": [],
        "msg": "success"
    }

    today = datetime.utcnow().date()
    current_month = today.replace(day=1)  # 当前月第一天

    # 构造每个月的起止时间（从当前月开始往前推）
    month_ranges = []
    for i in range(month_number):
        month_start = current_month - relativedelta(months=i)
        _, last_day = calendar.monthrange(month_start.year, month_start.month)
        month_end = month_start.replace(day=last_day)
        month_ranges.append((month_start, month_end))

    try:
        monthly_counts = []

        for start_date, end_date in month_ranges:
            # 构造该月的起始与结束时间（UTC）
            start_time = datetime.combine(start_date, datetime.min.time())
            end_time = datetime.combine(end_date, datetime.max.time())

            # 转为时间戳（毫秒）
            from_timestamp_ms = int(start_time.timestamp() * 1000)
            to_timestamp_ms = int(end_time.timestamp() * 1000)

            # 构建请求 URL
            url = f'{BASE_URL}{MESSAGE_ENDPOINT}{COUNT}?prev=1&from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'

            # 发起请求
            response = requests.get(
                url,
                headers=HEADERS,
                cookies=COOKIE,
                verify=False,
                timeout=10
            )

            if response.status_code != 200:
                raise Exception(f"外部接口错误：{response.text[:100]}")

            # 解析响应
            response_json = response.json()
            monthly_counts.append({
                "month": start_date.strftime("%Y-%m"),  # 格式化为 2025-05
                "count": response_json.get("doc_count", 0)
            })

        # 可选：按时间升序排列
        response_data["data"] = list(reversed(monthly_counts))
        return response_data

    except requests.Timeout:
        response_data.update({
            "code": "504",
            "msg": "请求外部接口超时"
        })
        return response_data, 504
    except requests.RequestException as e:
        response_data.update({
            "code": "500",
            "msg": f"请求失败：{str(e)}"
        })
        return response_data, 500
    except Exception as e:
        response_data.update({
            "code": "500",
            "msg": f"服务器内部错误：{str(e)}"
        })
        return response_data, 500