# 消息通知处理APP

## 功能描述
处理剧本执行结果并发送通知的APP，用于在业务剧本执行完成后自动判断是否需要生成通知。

## 使用方法

### 输入参数
- `script_type`: 剧本类型，支持以下值：
  - `s60000_pending`: S60000待处理剧本
  - `email`: 邮件剧本
  - `qianxin_ids`: 奇安信天眼和IDS剧本
  - `nsfocus_scan`: 绿盟漏扫剧本
  - `s60000_feedback`: S60000反馈截止剧本

- `result_data`: 业务APP的JSON返回结果（字符串格式），通过@(uuid.result)给出

- `execution_id`: 剧本执行ID（可选）

- `token`: 用户TOKEN（可选，不提供则自动获取系统TOKEN）

### 输出结果
```json
{
  "status": 0,  // 0表示成功，1表示失败
  "result": {
    "message": "通知处理成功",
    "notification_created": true,
    "notification_info": {
      "notification_id": "uuid",
      "title": "通知标题",
      "source": "来源系统",
      "count": 10
    }
  }
}
```

## 判断逻辑

### S60000待处理剧本 (s60000_pending)
- 判断字段: `processed_count`
- 条件: `processed_count != 0`
- 通知类型: "待处理数"

### 邮件剧本 (email)
- 判断字段: `mails_count`
- 条件: `mails_count != 0`
- 通知类型: "未读邮件"

### 奇安信天眼和IDS剧本 (qianxin_ids)
- 判断字段: `total`
- 条件: `total != 0`
- 通知类型: "危急告警"

### 绿盟漏扫剧本 (nsfocus_scan)
- 判断字段: `total_tasks` 和 `total_vulns`
- 条件: `total_tasks != 0` 或 `total_vulns != 0`
- 通知类型: "扫描任务" 或 "安全漏洞"

### S60000反馈截止剧本 (s60000_feedback)
- 判断字段: `ddl_entry_count`
- 条件: `ddl_entry_count != 0`
- 通知类型: "反馈截止"

## 配置说明

### TOKEN获取机制
APP支持两种TOKEN获取方式：

#### 方式1：用户提供TOKEN（推荐）
- 在APP参数中提供`token`参数
- 直接使用用户提供的TOKEN

#### 方式2：自动获取系统TOKEN
当用户未提供TOKEN时，APP会自动从数据库获取系统TOKEN：
1. 使用`Users.where('id', 1).first()`从数据库获取ID为1的用户
2. 验证token在Redis中是否有效
3. 如果有效则直接使用
4. 如果失效则自动生成新的token并更新到数据库和Redis
5. 如果获取失败，返回错误信息

### 依赖模块
APP使用项目现有的核心模块：
- `core.model.Users`: 用户模型
- `core.redis`: Redis连接
- `core.utils.randoms.Random`: 随机数生成
- `core.utils.times.Time`: 时间处理
- `loguru`: 用于日志记录
- `requests`: 用于HTTP请求

## 注意事项
1. 确保通知处理接口服务正常运行
2. 确保网络连接正常
3. 输入的JSON数据格式必须正确
4. 推荐在APP参数中提供有效的TOKEN
5. 如果不提供TOKEN，确保数据库中ID为1的用户存在
6. 如果不提供TOKEN，确保Redis服务正常运行
7. TOKEN获取失败时会返回明确的错误信息
