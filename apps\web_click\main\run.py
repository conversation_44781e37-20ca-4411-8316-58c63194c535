#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
import time
import ddddocr


async def access(ip, username="1", password="1"):
    logger.info("[网页访问] APP执行参数为: {ip} {username} {password}", ip=ip, username=username, password=password)
    
    try:
        # 创建 Chrome 浏览器驱动实例
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service)

        # 构建要访问的 URL
        url = f"http://{ip}"

        # 打开指定 URL 的网页
        driver.get(url)

        # 等待页面加载
        time.sleep(3)

        # 定位输入框并输入账号和密码
        username_input = driver.find_element(By.XPATH, '//*[@id="app"]/div[1]/form/div[1]/div/div[1]/input')
        password_input = driver.find_element(By.XPATH, '//*[@id="app"]/div[1]/form/div[2]/div/div/input')

        username_input.send_keys(username)
        password_input.send_keys(password)

        # 定位验证码图片元素
        captcha_img = driver.find_element(By.XPATH, '//*[@id="app"]/div[1]/form/div[3]/div/div[2]/img')

        # 获取图片的 base64 编码
        image = captcha_img.get_attribute('src').split(',')[-1]

        # 识别验证码
        ocr = ddddocr.DdddOcr()
        ocr.set_ranges("0123456789+-*/=?")
        result = ocr.classification(image, probability=True)
        s = ""
        for i in result['probability']:
            s += result['charsets'][i.index(max(i))]
        logger.info("[网页访问] 验证码原始识别结果: {s}", s=s)
        a = s[:3]
        captcha_result = eval(a)
        logger.info("[网页访问] 验证码计算结果: {result}", result=captcha_result)

        # 定位验证码输入框
        captcha_input = driver.find_element(By.XPATH, '//*[@id="app"]/div[1]/form/div[3]/div/div[1]/input')

        # 输入验证码
        captcha_input.send_keys(captcha_result)

        # 定位登录按钮
        login_button = driver.find_element(By.XPATH, '//*[@id="app"]/div[1]/form/div[4]/div/button')

        # 点击登录按钮
        login_button.click()
        logger.info("[网页访问] 完成登录操作")

        # 返回成功结果
        return {"status": 0, "result": {"message": "登录成功", "ip": ip}}

    except Exception as e:
        logger.error("[网页访问] 发生错误: {e}", e=e)
        return {"status": 2, "result": f"访问网页失败: {str(e)}"}
    finally:
        if 'driver' in locals():
            # 关闭浏览器
            driver.quit()
            logger.info("[网页访问] 已关闭浏览器") 