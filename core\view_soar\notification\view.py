#!/usr/bin/env python
# encoding:utf-8
from . import *
import uuid
import json
from datetime import datetime

# 剧本条件映射配置
SCRIPT_CONDITIONS = {
    "s60000_pending": {
        "field": "processed_count",
        "type": "待处理数",
        "source": "S60000系统"
    },
    "email": {
        "field": "mails_count", 
        "type": "未读邮件",
        "source": "邮件系统"
    },
    "qianxin_ids": {
        "field": "total",
        "type": "危急告警",
        "source": "奇安信天眼"
    },
    "nsfocus_scan": {
        "field": "total_tasks",
        "type": "扫描任务",
        "source": "绿盟漏扫"
    },
    "s60000_feedback": {
        "field": "processed_count",
        "type": "反馈截止",
        "source": "S60000系统"
    }
}


def generate_notification_title(source, notification_type, count, timestamp=None):
    """生成通知标题"""
    if timestamp is None:
        timestamp = datetime.now()
    
    format_time = timestamp.strftime("%Y/%m/%d %H:%M")
    return f"{format_time}发现【{source}】存在{notification_type}({count})"


def process_script_result(script_type, result_data):
    """处理剧本结果，判断是否需要通知"""
    """ TODO:
      1.根据业务类型，解析相应的JSON
      2.把每次解析的通知相关的数据，存入mysql，需要先建表
      3.判断是否需要通知
    """
    if script_type not in SCRIPT_CONDITIONS:
        return False, None, None, None, None
    
    config = SCRIPT_CONDITIONS[script_type]
    field = config["field"]
    _type = config["type"]
    notifications = []

    count = result_data.get(field, 0)
    if count == 0:
        return False, None, None, None, None
    
    # 处理绿盟漏扫的特殊情况（多个字段）
    if script_type == "nsfocus_scan":
    
        notifications.append({
            "count": count,
            "type": config["type"],
            "source": config["source"]
          })
        return len(notifications) > 0, notifications, config["source"], None, None
    
    # 处理单字段情况
    count = result_data.get(config["field"], 0)
    if count > 0:
        return True, count, config["source"], config["type"], config["field"]
    
    return False, None, None, None, None


def save_notification_to_redis(user_id, notification_data):
    """保存通知到Redis"""
    notification_id = str(uuid.uuid4())
    
    # 保存通知详情
    redis.hset(
        f"notification:detail:{notification_id}",
        mapping={
            "title": notification_data["title"],
            "source": notification_data["source"],
            "notification_type": notification_data["notification_type"],
            "count": str(notification_data["count"]),
            "status": "unread",
            "created_at": notification_data["created_at"],
            "result_data": json.dumps(notification_data["result_data"]),
            "user_id": str(user_id)
        }
    )
    
    # 添加到用户通知队列
    redis.lpush(f"notification:queue:{user_id}", notification_id)
    
    # 更新未读计数
    redis.incr(f"notification:unread:{user_id}")
    
    # 设置过期时间（30天）
    redis.expire(f"notification:detail:{notification_id}", 30 * 24 * 3600)
    
    return notification_id


def get_user_notifications(user_id, page=1, limit=20, status="all"):
    """获取用户通知列表"""
    # 获取通知ID列表
    start = (page - 1) * limit
    end = start + limit - 1
    notification_ids = redis.lrange(f"notification:queue:{user_id}", start, end)
    
    notifications = []
    for notification_id in notification_ids:
        notification_data = redis.hgetall(f"notification:detail:{notification_id}")
        if notification_data:
            # 状态过滤
            if status != "all" and notification_data.get("status") != status:
                continue
                
            notifications.append({
                "notification_id": notification_id,
                "title": notification_data.get("title"),
                "source": notification_data.get("source"),
                "notification_type": notification_data.get("notification_type"),
                "count": int(notification_data.get("count", 0)),
                "status": notification_data.get("status"),
                "created_at": notification_data.get("created_at"),
                "result_data": json.loads(notification_data.get("result_data", "{}"))
            })
    
    # 获取总数
    total = redis.llen(f"notification:queue:{user_id}")
    
    return {
        "total": total,
        "page": page,
        "limit": limit,
        "notifications": notifications
    }


@r.route("/notification/process", methods=['POST'])
def notification_process():
    """
    消息处理接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 剧本执行结果处理
        schema:
          type: object
          required:
            - script_type
            - result
          properties:
            script_type:
              type: string
              description: 剧本类型
            result:
              type: object
              description: 业务APP的JSON返回结果
            execution_id:
              type: string
              description: 剧本执行ID
            timestamp:
              type: string
              description: 执行时间戳
    responses:
      200:
        description: 处理成功
      400:
        description: 参数错误
      500:
        description: 处理失败
    """
    try:
        script_type = request.json.get("script_type", "")
        result_data = request.json.get("result", {})
        execution_id = request.json.get("execution_id", "")
        timestamp_str = request.json.get("timestamp", "")
        
        if not script_type or not result_data:
            return Response.re(err=Err)
        
        # 解析时间戳
        timestamp = datetime.now()
        if timestamp_str:
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
            except:
                pass
        
        # 处理剧本结果
        need_notification, count_or_list, source, notification_type, field = process_script_result(script_type, result_data)
        
        if not need_notification:
            return Response.re(data={
                "need_notification": False,
                "message": "无需通知"
            })
        
        # 获取当前用户ID（从token中解析）
        token = request.headers.get('token', '')
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 处理绿盟漏扫的多通知情况
        if script_type == "nsfocus_scan" and isinstance(count_or_list, list):
            notification_ids = []
            for notification_info in count_or_list:
                title = generate_notification_title(
                    notification_info["source"], 
                    notification_info["type"], 
                    notification_info["count"], 
                    timestamp
                )
                
                notification_data = {
                    "title": title,
                    "source": notification_info["source"],
                    "notification_type": notification_info["type"],
                    "count": notification_info["count"],
                    "created_at": timestamp.isoformat(),
                    "result_data": result_data,
                    "execution_id": execution_id
                }
                
                notification_id = save_notification_to_redis(user_id, notification_data)
                notification_ids.append(notification_id)
            
            return Response.re(data={
                "need_notification": True,
                "notification_ids": notification_ids,
                "count": len(notification_ids),
                "source": source,
                "created_at": timestamp.isoformat()
            })
        
        # 处理单通知情况
        title = generate_notification_title(source, notification_type, count_or_list, timestamp)
        
        notification_data = {
            "title": title,
            "source": source,
            "notification_type": notification_type,
            "count": count_or_list,
            "created_at": timestamp.isoformat(),
            "result_data": result_data,
            "execution_id": execution_id
        }
        
        notification_id = save_notification_to_redis(user_id, notification_data)
        
        return Response.re(data={
            "need_notification": True,
            "notification_id": notification_id,
            "notification_type": notification_type,
            "count": count_or_list,
            "source": source,
            "title": title,
            "created_at": timestamp.isoformat()
        })
        
    except Exception as e:
        logger.error(f"通知处理异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/notification/list", methods=['POST'])
def notification_list():
    """
    通知列表查询接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 查询参数
        schema:
          type: object
          properties:
            page:
              type: integer
              description: 页码
              default: 1
            limit:
              type: integer
              description: 每页数量
              default: 20
            status:
              type: string
              description: 状态过滤
              enum: ["all", "read", "unread"]
              default: "all"
    responses:
      200:
        description: 查询成功
      400:
        description: 参数错误
      500:
        description: 查询失败
    """
    try:
        page = request.json.get("page", 1)
        limit = request.json.get("limit", 20)
        status = request.json.get("status", "all")
        
        # 获取当前用户ID
        token = request.headers.get('token', '')
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 获取通知列表
        result = get_user_notifications(user_id, page, limit, status)
        
        return Response.re(data=result)
        
    except Exception as e:
        logger.error(f"通知列表查询异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/notification/status", methods=['PUT'])
def notification_status():
    """
    通知状态更新接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 状态更新参数
        schema:
          type: object
          required:
            - notification_id
            - status
          properties:
            notification_id:
              type: string
              description: 通知ID
            status:
              type: string
              description: 新状态
              enum: ["read", "unread"]
    responses:
      200:
        description: 更新成功
      400:
        description: 参数错误
      404:
        description: 通知不存在
      500:
        description: 更新失败
    """
    try:
        notification_id = request.json.get("notification_id", "")
        status = request.json.get("status", "")

        if not notification_id or status not in ["read", "unread"]:
            return Response.re(err=Err)

        # 获取当前用户ID
        token = request.headers.get('token', '')
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)

        # 检查通知是否存在且属于当前用户
        notification_data = redis.hgetall(f"notification:detail:{notification_id}")
        if not notification_data or notification_data.get("user_id") != str(user_id):
            return Response.re(err=Err)

        # 获取原状态
        old_status = notification_data.get("status", "unread")

        # 更新状态
        redis.hset(f"notification:detail:{notification_id}", "status", status)

        # 更新未读计数
        if old_status == "unread" and status == "read":
            redis.decr(f"notification:unread:{user_id}")
        elif old_status == "read" and status == "unread":
            redis.incr(f"notification:unread:{user_id}")

        return Response.re(data={
            "notification_id": notification_id,
            "status": status,
            "updated_at": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"通知状态更新异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/notification/dialog", methods=['POST'])
def notification_dialog():
    """TODO: 根据模型实际的对话生成接口，进行调整"""
    """
    通知对话生成接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 对话生成参数
        schema:
          type: object
          required:
            - notification_id
          properties:
            notification_id:
              type: string
              description: 通知ID
            user_question:
              type: string
              description: 用户问题
    responses:
      200:
        description: 对话生成成功
      400:
        description: 参数错误
      404:
        description: 通知不存在
      500:
        description: 对话生成失败
    """
    try:
        notification_id = request.json.get("notification_id", "")
        user_question = request.json.get("user_question", "")

        if not notification_id:
            return Response.re(err=Err)

        # 获取当前用户ID
        token = request.headers.get('token', '')
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)

        # 获取通知详情
        notification_data = redis.hgetall(f"notification:detail:{notification_id}")
        if not notification_data or notification_data.get("user_id") != str(user_id):
            return Response.re(err=Err)

        # 构造对话上下文
        context_info = {
            "通知标题": notification_data.get("title"),
            "来源系统": notification_data.get("source"),
            "通知类型": notification_data.get("notification_type"),
            "数量": notification_data.get("count"),
            "创建时间": notification_data.get("created_at"),
            "详细数据": json.loads(notification_data.get("result_data", "{}"))
        }

        # 生成系统提示词
        system_prompt = f"""你是一个安全运营助手，正在帮助用户分析以下安全通知：

通知信息：
- 标题：{context_info['通知标题']}
- 来源：{context_info['来源系统']}
- 类型：{context_info['通知类型']}
- 数量：{context_info['数量']}
- 时间：{context_info['创建时间']}

详细数据：{json.dumps(context_info['详细数据'], ensure_ascii=False, indent=2)}

请基于以上信息，为用户提供专业的安全分析和建议。如果用户没有具体问题，请主动分析这个安全事件的重要性、可能的影响和建议的处理步骤。"""

        # 构造对话内容
        conversation = [
            {
                "role": "system",
                "content": system_prompt,
                "timestamp": datetime.now().isoformat()
            }
        ]

        # 如果用户有问题，添加用户消息
        if user_question:
            conversation.append({
                "role": "user",
                "content": user_question,
                "timestamp": datetime.now().isoformat()
            })

            # 这里可以调用现有的大模型接口生成回复
            # 暂时返回一个示例回复
            ai_response = f"基于您的通知「{notification_data.get('title')}」，我建议您立即关注此{notification_data.get('notification_type')}。具体分析和处理建议请查看详细数据。"

            conversation.append({
                "role": "assistant",
                "content": ai_response,
                "timestamp": datetime.now().isoformat()
            })

        # 生成对话ID
        dialog_id = str(uuid.uuid4())

        return Response.re(data={
            "dialog_id": dialog_id,
            "conversation": conversation,
            "context": {
                "notification_info": {
                    "notification_id": notification_id,
                    "title": notification_data.get("title"),
                    "source": notification_data.get("source"),
                    "notification_type": notification_data.get("notification_type"),
                    "count": int(notification_data.get("count", 0)),
                    "created_at": notification_data.get("created_at")
                },
                "result_data": json.loads(notification_data.get("result_data", "{}"))
            }
        })

    except Exception as e:
        logger.error(f"通知对话生成异常: {str(e)}")
        return Response.re(err=Err)
