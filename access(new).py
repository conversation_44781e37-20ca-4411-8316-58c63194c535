#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
# 功能要求：登录该新准入系统，根据传入的ip，获取终端名称，MAC地址，备注名称

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import re

# 1.模拟登录
# 用户名 Xpath

# 2.选中text中包含输入参数ip前24位的子网网段的元素点击其tr中最后一个td的"编辑按钮"，

# 3.搜索text中为输入参数ip的td
# 搜出所有的<tr class="q-table__row">元素
# 取其中的第三个td与输入参数对比，相同的，取第二个td的text，就是MAC
# 取第四个td的text，备注名称

# 4.返回结果
# 返回结果为json格式，包含终端名称，MAC地址，备注名称

# 定义XPath和其他选择器常量
LOGIN_USERNAME_XPATH = "//input[@name='username']"  
LOGIN_PASSWORD_XPATH = "//input[@name='password']" 
LOGIN_BUTTON_XPATH = "//input[@type='submit']"  
SUBNET_ROWS_XPATH = "//tr[contains(@class, 'subnet-row')]"  
EDIT_BUTTON_XPATH = ".//td[last()]/button"  
TABLE_ROWS_XPATH = "//tr[@class='q-table__row']" 

# 登录凭据
ACCESS_USERNAME = "admin"  # 用户名
ACCESS_PASSWORD = "password"  # 密码
ACCESS_URL = "http://"  # URL

async def get_device_info(ip):
    """
    使用Selenium操控无头浏览器登录准入系统并获取设备信息
    
    Args:
        ip: 要查询的IP地址
        
    Returns:
        dict: 包含操作状态和结果的字典，结果包含终端名称、MAC地址和备注名称
    """
    logger.info("[准入系统] APP执行参数为: {ip}", ip=ip)
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    
    try:
        # 初始化WebDriver
        logger.info("[准入系统] 初始化WebDriver")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置等待
        wait = WebDriverWait(driver, 10)
        
        # 访问准入系统登录页面
        logger.info("[准入系统] 访问准入系统登录页面")
        driver.get(ACCESS_URL)
        
        # 1. 登录操作
        logger.info("[准入系统] 执行登录操作")
        username_input = wait.until(EC.presence_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH)))
        password_input = driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH)
        
        username_input.send_keys(ACCESS_USERNAME)
        password_input.send_keys(ACCESS_PASSWORD)
        
        login_button = driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH)
        login_button.click()
        
        # 等待登录成功
        time.sleep(2)
        
        # 2. 提取IP前24位，用于匹配子网
        ip_prefix = '.'.join(ip.split('.')[:3])
        logger.info("[准入系统] 查找子网: {prefix}", prefix=ip_prefix)
        
        # 查找所有子网行
        subnet_rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, SUBNET_ROWS_XPATH)))
        target_row = None
        
        # 找到包含指定IP前缀的子网行
        for row in subnet_rows:
            if ip_prefix in row.text:
                target_row = row
                break
        
        if not target_row:
            logger.error("[准入系统] 未找到包含IP前缀的子网: {prefix}", prefix=ip_prefix)
            return {"status": 1, "result": f"未找到包含IP前缀的子网: {ip_prefix}"}
        
        # 点击编辑按钮
        edit_button = target_row.find_element(By.XPATH, EDIT_BUTTON_XPATH)
        edit_button.click()
        
        # 等待表格加载
        time.sleep(2)
        
        # 3. 搜索包含指定IP的表格行
        logger.info("[准入系统] 查找IP对应的设备信息: {ip}", ip=ip)
        table_rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, TABLE_ROWS_XPATH)))
        
        device_info = {"device_name": "", "mac_address": "", "remarks": ""}
        found = False
        
        for row in table_rows:
            # 获取所有单元格
            cells = row.find_elements(By.TAG_NAME, "td")
            
            # 检查是否有足够的单元格
            if len(cells) >= 4:
                # 第三个单元格是IP地址
                cell_ip = cells[2].text.strip()
                
                # 如果找到匹配的IP
                if cell_ip == ip:
                    # 获取设备信息（终端名称、MAC地址、备注名称）
                    device_info["device_name"] = cells[0].text.strip()  # 假设第一个单元格是终端名称
                    device_info["mac_address"] = cells[1].text.strip()  # 第二个单元格是MAC地址
                    device_info["remarks"] = cells[3].text.strip()  # 第四个单元格是备注名称
                    found = True
                    break
        
        if not found:
            logger.error("[准入系统] 未找到IP对应的设备信息: {ip}", ip=ip)
            return {"status": 1, "result": f"未找到IP对应的设备信息: {ip}"}
        
        # 4. 返回结果
        logger.info("[准入系统] 成功获取设备信息: {info}", info=device_info)
        return {"status": 0, "result": device_info}
        
    except Exception as e:
        logger.error("[准入系统] 操作失败: {e}", e=e)
        return {"status": 2, "result": f"准入系统操作失败: {str(e)}"}
        
    finally:
        # 确保浏览器关闭
        if driver:
            logger.info("[准入系统] 关闭WebDriver")
            driver.quit()




