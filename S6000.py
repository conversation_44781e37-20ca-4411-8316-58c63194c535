#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
# 1. 登录 无验证码
# 2. 点击工作联系单 Xpath
# 3. 选定 tbody Xpath  ，取每个下属tr，取tr中的第七个td的text值，
# 可能为：未反馈、已反馈，取其中未反馈的tr，将其第八个的查看按钮（用text标注），点击
# 4. 换页
# 5. 重复3

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 定义XPath和其他选择器常量
LOGIN_USERNAME_XPATH = "//input[@name='username']"
LOGIN_PASSWORD_XPATH = "//input[@name='password']"
LOGIN_BUTTON_XPATH = "//input[@type='submit']"
WORK_ORDER_LINK_XPATH = "//a[contains(text(),'工作联系单')]"
TABLE_TBODY_XPATH = "//table[@id='workOrderTable']/tbody"  # 表格tbody的XPath
NEXT_PAGE_BUTTON_XPATH = "//a[contains(text(),'下一页')]"  # 下一页按钮XPath
LAST_PAGE_INDICATOR_XPATH = "//a[contains(@class,'disabled') and contains(text(),'下一页')]"  # 判断是否最后一页的XPath

# 登录凭据
S6000_USERNAME = "admin"  # 用户名
S6000_PASSWORD = "password"  # 密码
S6000_URL = "http://系统地址"  # URL

async def process_work_orders():
    """
    使用Selenium操控无头浏览器处理S6000系统中的工作联系单
    
    Returns:
        dict: 包含操作状态和结果的字典
    """
    logger.info("[S6000系统] 开始处理工作联系单")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    processed_orders = []
    
    try:
        # 初始化WebDriver
        logger.info("[S6000系统] 初始化WebDriver")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置等待
        wait = WebDriverWait(driver, 10)
        
        # 访问登录页面
        logger.info("[S6000系统] 访问登录页面")
        driver.get(S6000_URL)
        
        # 1. 登录操作
        logger.info("[S6000系统] 执行登录操作")
        username_input = wait.until(EC.presence_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH)))
        password_input = driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH)
        
        username_input.send_keys(S6000_USERNAME)
        password_input.send_keys(S6000_PASSWORD)
        
        login_button = driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH)
        login_button.click()
        
        # 等待登录成功
        time.sleep(2)
        
        # 2. 点击工作联系单
        logger.info("[S6000系统] 点击工作联系单")
        work_order_link = wait.until(EC.element_to_be_clickable((By.XPATH, WORK_ORDER_LINK_XPATH)))
        work_order_link.click()
        
        # 等待页面加载
        time.sleep(2)
        
        page_num = 1
        has_next_page = True
        
        # 处理所有页面
        while has_next_page:
            logger.info("[S6000系统] 处理第 {page} 页工作联系单", page=page_num)
            
            # 3. 处理表格中的行
            tbody = wait.until(EC.presence_of_element_located((By.XPATH, TABLE_TBODY_XPATH)))
            rows = tbody.find_elements(By.TAG_NAME, "tr")
            
            # 遍历每一行
            for row in rows:
                try:
                    # 获取第七列的状态文本
                    status_cell = row.find_elements(By.TAG_NAME, "td")[6]  # 索引6表示第七个td
                    status = status_cell.text.strip()
                    
                    # 如果状态为"未反馈"，则点击"查看"按钮
                    if status == "未反馈":
                        # 获取该行的工单号或其他标识信息（假设在第一列）
                        order_id = row.find_elements(By.TAG_NAME, "td")[0].text.strip()
                        logger.info("[S6000系统] 发现未反馈工单: {id}", id=order_id)
                        
                        # 点击查看按钮（第八列）
                        view_button = row.find_elements(By.TAG_NAME, "td")[7].find_element(By.LINK_TEXT, "查看")
                        view_button.click()
                        
                        # 等待详情页面加载
                        time.sleep(2)
                        
                        # 这里可以添加处理工单详情的逻辑
                        # ...
                        
                        # 返回工单列表页面
                        driver.back()
                        time.sleep(2)
                        
                        # 重新获取表格元素（避免StaleElementReferenceException）
                        tbody = wait.until(EC.presence_of_element_located((By.XPATH, TABLE_TBODY_XPATH)))
                        rows = tbody.find_elements(By.TAG_NAME, "tr")
                        
                        # 记录已处理的工单
                        processed_orders.append(order_id)
                except Exception as e:
                    logger.error("[S6000系统] 处理工单行时出错: {e}", e=e)
                    continue
            
            # 4. 判断是否有下一页
            try:
                # 检查"下一页"按钮是否存在且不是禁用状态
                if driver.find_elements(By.XPATH, LAST_PAGE_INDICATOR_XPATH):
                    has_next_page = False
                    logger.info("[S6000系统] 已到达最后一页")
                else:
                    # 点击下一页
                    next_page_button = driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH)
                    next_page_button.click()
                    time.sleep(2)  # 等待新页面加载
                    page_num += 1
            except Exception as e:
                logger.error("[S6000系统] 翻页操作失败: {e}", e=e)
                has_next_page = False
        
        logger.info("[S6000系统] 工作联系单处理完成，共处理 {count} 个工单", count=len(processed_orders))
        return {"status": 0, "result": f"成功处理 {len(processed_orders)} 个未反馈工单", "processed_orders": processed_orders}
        
    except Exception as e:
        logger.error("[S6000系统] 操作失败: {e}", e=e)
        return {"status": 2, "result": f"S6000系统操作失败: {str(e)}"}
        
    finally:
        # 确保浏览器关闭
        if driver:
            logger.info("[S6000系统] 关闭WebDriver")
            driver.quit()