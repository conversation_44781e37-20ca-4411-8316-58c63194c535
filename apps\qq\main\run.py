#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import pyautogui
import time
import os


async def open_qq(msg):
    """
    使用图像识别定位+模拟点击的方式打开QQ

    Args:
        msg (str): 要发送的消息内容
        
    Returns:
        dict: 包含操作状态和结果的字典
    """
    logger.info("[QQ] APP执行参数为: {msg}", msg=msg)
    try:
        # 检查图片文件是否存在
        icon_path = 'qq_icon.png'
        if not os.path.exists(icon_path):
            logger.info(f"[QQ] 出现错误：图片文件 '{icon_path}' 不存在，请检查路径")
            return {"status": 2, "result": f"图片文件 '{icon_path}' 不存在，请检查路径"}
            
        # 尝试定位 QQ 图标
        qq_icon_location = pyautogui.locateOnScreen(icon_path, confidence=0.9)
        if qq_icon_location is None:
            logger.info(f"[QQ] 出现错误：未找到QQ图标，请确认图片与屏幕内容匹配")
            return {"status": 2, "result": f"未找到 QQ 图标，请确认图片与屏幕内容匹配"}

        # 获取图标中心位置
        qq_icon_x, qq_icon_y = pyautogui.center(qq_icon_location)
        logger.info(f"[QQ] 已获取 QQ 图标位置：({qq_icon_x}, {qq_icon_y})")

        # 模拟鼠标移动到 QQ 图标位置
        pyautogui.moveTo(qq_icon_x, qq_icon_y, duration=1)

        # 模拟鼠标左键点击
        pyautogui.click()
        logger.info("[QQ] 已尝试点击 QQ 图标打开 QQ")

        # 等待 QQ 打开
        time.sleep(2)

        # 定位搜索框图标search_icon.png
        search_icon_path = 'search_icon.png'
        if not os.path.exists(search_icon_path):
            logger.info(f"[QQ] 出现错误：图片文件 '{search_icon_path}' 不存在，请检查路径")
            return {"status": 2, "result": f"图片文件 '{search_icon_path}' 不存在，请检查路径"}

        search_icon_location = pyautogui.locateOnScreen(search_icon_path, confidence=0.9)
        if search_icon_location is None:
            logger.info(f"[QQ] 出现错误：未找到搜索框图标，请确认图片与屏幕内容匹配")
            return {"status": 2, "result": f"未找到搜索框图标，请确认图片与屏幕内容匹配"}

        # 获取搜索框图标中心位置
        search_icon_x, search_icon_y = pyautogui.center(search_icon_location)
        logger.info(f"[QQ] 已获取搜索框图标位置：({search_icon_x}, {search_icon_y})")

        # 模拟鼠标移动到搜索框图标位置
        pyautogui.moveTo(search_icon_x, search_icon_y, duration=1)

        # 模拟鼠标左键点击
        pyautogui.click()
        logger.info("[QQ] 已尝试点击搜索框")

        # 输入好友 ID
        pyautogui.typewrite("lx", interval=0.1)
        logger.info("[QQ] 已输入好友 ID 'lx'")
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(1)

        # 定位好友头像图标
        lx_icon_path = 'lx_icon.png'
        if not os.path.exists(lx_icon_path):
            logger.info(f"[QQ] 出现错误：图片文件 '{lx_icon_path}' 不存在，请检查路径")
            return {"status": 2, "result": f"图片文件 '{lx_icon_path}' 不存在，请检查路径"}

        lx_icon_location = pyautogui.locateOnScreen(lx_icon_path, confidence=0.9)
        if lx_icon_location is None:
            logger.info(f"[QQ] 出现错误：未找到好友头像图标，请确认图片与屏幕内容匹配")
            return {"status": 2, "result": f"未找到好友头像图标，请确认图片与屏幕内容匹配"}

        # 获取好友头像中心位置
        lx_icon_x, lx_icon_y = pyautogui.center(lx_icon_location)
        logger.info(f"[QQ] 已获取好友头像图标位置：({lx_icon_x}, {lx_icon_y})")

        # 模拟鼠标移动到好友头像图标位置
        pyautogui.moveTo(lx_icon_x, lx_icon_y, duration=1)

        # 模拟鼠标左键点击
        pyautogui.click()
        logger.info("[QQ] 已点击好友头像进入聊天")

        # 进入聊天界面后等待
        time.sleep(2)

        # 输入消息msg
        pyautogui.typewrite(msg, interval=0.1)
        pyautogui.press('enter')
        logger.info("[QQ] 已输入消息")

        # 按下回车键发送消息
        pyautogui.press('enter')
        logger.info("[QQ] 已发送消息")
        return {"status": 0, "result": f"QQ消息发送成功: '{msg}'"}

    except Exception as e:
        logger.error("[QQ] 出现错误: {e}", e=e)
        return {"status": 2, "result": f"QQ点击操作失败: {str(e)}"} 