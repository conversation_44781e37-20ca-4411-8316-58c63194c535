   # 例如：from core.view_decision import r as r_decision
    from core.view_decision.decision_chat import r as r_chat# 用户与大模型对话路由
    from core.view_decision.decision_create import r as r_create#创建新对话路由
    from core.view_decision.decision_delete import r as r_delete  #删除指定对话路由
    from core.view_decision.decision_clear import r as r_clear # 清除模型记忆路由
    from core.view_decision.decision_bug import r as r_bug # 提交报错功能路由
    from core.view_decision.decision_apikey import r as r_apikey  #生成APIKEY路由
    from core.view_decision.decision_hiskey import r as r_hiskey#查询历史APIKEY路由
    from core.view_decision.decision_apichat import r as r_apichat#API功能实现路由
    from core.view_decision.decision_num import r as r_num#姿态大屏大模型接口路由
    from core.view_decision.decision_deletekey import r as r_deletekey#清除已生成的APIKEY路由
    decision_route_list = [r_chat,r_create,r_delete,r_clear,r_bug,r_apikey,r_hiskey,r_apichat,r_num,r_deletekey] 

    decision_no_path = [
        "/api/v1/decision/decision_chat",
        "/api/v1/decision/chat",#用户与大模型交互对话 接口1
        "/api/v1/decision/create",#用户创建新的对话 接口2
        "/api/v1/decision/delete",#用户删除指定对话 接口3
        "/api/v1/decision//memory/<user_id>",#清除历史记忆 接口4
        "/api/v1/decision/bug-report",#提交BUG信息  接口5
        "/api/v1/decision/apikey",#生成专属APIKEY 接口6
        "/api/v1/decision/hisapikeys/<user_id>",#查询历史APIKEY 接口7
        "/api/v1/decision/apichat",#APIKEY验证与使用 接口8
        "/api/v1/decision/chat_num/<user_id>",#姿态大屏大模型 接口9
        "/api/v1/decision/deletekey/<user_id>",#删除历史APIKEY 接口10
    ]

一些需要注意的事项以及对部分重要原理的解释。
（1）关于接口1： 
	接口一里的模型引用名暂时还是  "model": "llama3.2:3b",因为测试用，我自己笔记本上跑不了第三组训练的模型，等到时候正式和前端对接，且模型已经部署到云服务器的时候，改下模型名就可以了，LLAMA的调用方式还是比较简单的。
	而且关于聊天的记忆机制功能的实现，在用户提交 USRE_ID SESSIONID MESSSAGE时，我们会在USER_CHAT表里添加其其内容 ，表里有HISTEXT，就是代表着用户与AI的历史对话记录，每次用户提问时，都会把HISTEXT和新问题一起喂给大模型（这是比较常见的做法 GPT DEEPSEEK等都是这种机制，不过GPT的性能肯定比咱们的强很多，支持128000TOKEN   咱们的是8000TOKEN），然后当然因为对话的增加，为了保证模型性能，需要作摘要和轮数截断，在贮存历史对话时， 首先检测字符数是否到达5000 ，如果到达， 就检测对话轮数  ，如果对话轮数小于等于8轮， 则进行摘要处理 ，用模型提炼当前内容的摘要 ，使之精简到600字符，如果对话轮数大于等于9轮 则进行按轮截断处理，优先保留后七轮对话。这样做能够在保证性能和记忆力的前提下最好的处理问题。
（2）关于接口2：
        用户在打开“大模型”页面时，因为当前页面必定会有一个默认可用的新对话，所以在打开时就可以触发接口2，为当前对话生成对应SESSION_ID
（3）关于接口4：
	此处清除记忆的机理就是，删除指定USER_ID存储在CHAT表里的所有内容，这样模型就什么都记不得了。
（4）关于接口5：
	BUG提交图片里，提交后的图片是以二进制代码的形式储存在MYSQL里，如果到时候要为了运维做图片回显，可以再议
（5）关于接口7：
	查询后会返回当前用户已经生成的所有APIKEY 所以无需做定向查询，前端有文本回显就可以了。
（6）关于接口8：
        这个接口应该不是大模型页面里面有的，但是为了保证用户生成的APIKEY可以使用，我就做了这个功能，与前端页面无关，只是为了保证这个功能可用。
（7）大模型所用的w5_db里的三个表结构：
**********************************************************************************************************************************
mysql> DESC user_bug;
+-----------------+----------+------+-----+---------+----------------+
| Field           | Type     | Null | Key | Default | Extra          |
+-----------------+----------+------+-----+---------+----------------+
| user_id         | int      | NO   | PRI | NULL    | auto_increment |
| bug_description | text     | NO   |     | NULL    |                |
| bug_image       | longblob | YES  |     | NULL    |                |
+-----------------+----------+------+-----+---------+----------------+
********************************************************************************************************************************
mysql> DESC user_apikeys;
+------------+--------------+------+-----+---------+-------+
| Field      | Type         | Null | Key | Default | Extra |
+------------+--------------+------+-----+---------+-------+
| user_id    | int          | NO   | PRI | NULL    |       |
| api_key    | varchar(255) | NO   | PRI | NULL    |       |
| session_id | int          | NO   | PRI | NULL    |       |
+------------+--------------+------+-----+---------+-------+
3 rows in set (0.01 sec)
**********************************************************************************************************************************
mysql> DESC user_chat;
+------------+--------------+------+-----+---------+-------+
| Field      | Type         | Null | Key | Default | Extra |
+------------+--------------+------+-----+---------+-------+
| user_id    | varchar(255) | NO   | PRI | NULL    |       |
| session_id | varchar(255) | NO   | PRI | NULL    |       |
| histext    | longtext     | YES  |     | NULL    |       |
+------------+--------------+------+-----+---------+-------+
3 rows in set (0.00 sec)

