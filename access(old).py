#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
# 功能要求：根据传入的ip值，获取终端名称，MAC地址，备注名称
# 用selenium

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
import time
import re

# 定义XPath和其他选择器常量
LOGIN_USERNAME_XPATH = "//input[@name='loginname']"
LOGIN_PASSWORD_XPATH = "//input[@name='loginpassword']"
CAPTCHA_XPATH = "//span[@id='span_keyImg']"
LOGIN_BUTTON_XPATH = "//input[@type='submit']" 
ACCESS_SYSTEM_BUTTON_XPATH = "//a[@class='notclick'][contains(text(),'准入系统')]"
IP_BINDING_LINK_TEXT = "IP绑定"
SHOW_HIDE_IMG_XPATH = "//img[@onclick=\"showHide('a')\"]"
IP_INPUT_XPATH = "//input[@id='ip']"
TERMINAL_NAME_XPATH = "//tr[@id='ch_tr0']/td[7]/div"
DEVICE_MANAGEMENT_LINK_TEXT = "设备管理"
ADVANCED_SEARCH_LINK_TEXT = "高级检索"
TERM_NAME_INPUT_XPATH = "//input[@name='termname'][@id='termname']"
ONLINE_SELECT_XPATH = "//select[@name='online']"
SEARCH_BUTTON_XPATH = "//input[@value='查找']"
MAC_ADDRESS_XPATH = "//td[contains(text(),'MAC地址')]/following-sibling::td[1]"

# 登录凭据
ACCESS_USERNAME = "wabadmin"
ACCESS_PASSWORD = "Whgd@95598"
ACCESS_URL = "http://"  # URL

async def get_device_info_old(ip):
    """
    使用Selenium操控无头浏览器登录旧准入系统并获取设备信息
    
    Args:
        ip: 要查询的IP地址
        
    Returns:
        dict: 包含操作状态和结果的字典，结果包含终端名称、MAC地址和备注名称
    """
    logger.info("[旧准入系统] APP执行参数为: {ip}", ip=ip)
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    
    try:
        # 初始化WebDriver
        logger.info("[旧准入系统] 初始化WebDriver")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置等待
        wait = WebDriverWait(driver, 10)
        
        # 访问准入系统登录页面
        logger.info("[旧准入系统] 访问准入系统登录页面")
        driver.get(ACCESS_URL)
        
        # 1. 登录操作
        logger.info("[旧准入系统] 执行登录操作")
        username_input = wait.until(EC.presence_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH)))
        password_input = driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH)
        
        username_input.send_keys(ACCESS_USERNAME)
        password_input.send_keys(ACCESS_PASSWORD)
        
        # TODO: 验证码处理，可能需要OCR服务或手动输入
        # 这里假设验证码能够自动识别，实际使用时需要替换为合适的验证码处理方式
        captcha = driver.find_element(By.XPATH, CAPTCHA_XPATH)
        captcha_text = "识别的验证码"  # 实际应用中需要替换为验证码识别逻辑
        
        # 假设有一个输入验证码的字段
        captcha_input = driver.find_element(By.XPATH, "//input[@name='captcha']")  # 替换为实际XPath
        captcha_input.send_keys(captcha_text)
        
        login_button = driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH)
        login_button.click()
        
        # 等待登录成功
        time.sleep(2)
        
        # 2. 点击准入系统按钮
        logger.info("[旧准入系统] 点击准入系统按钮")
        access_system_button = wait.until(EC.element_to_be_clickable((By.XPATH, ACCESS_SYSTEM_BUTTON_XPATH)))
        access_system_button.click()
        
        # 3. 点击IP绑定
        logger.info("[旧准入系统] 点击IP绑定按钮")
        ip_binding_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, IP_BINDING_LINK_TEXT)))
        ip_binding_link.click()
        
        # 4. 点击显示/隐藏图标
        logger.info("[旧准入系统] 点击显示/隐藏图标")
        show_hide_img = wait.until(EC.element_to_be_clickable((By.XPATH, SHOW_HIDE_IMG_XPATH)))
        show_hide_img.click()
        
        # 5. 输入IP并回车查找
        logger.info("[旧准入系统] 输入IP并查找: {ip}", ip=ip)
        ip_input = wait.until(EC.presence_of_element_located((By.XPATH, IP_INPUT_XPATH)))
        ip_input.clear()
        ip_input.send_keys(ip)
        ip_input.send_keys(Keys.RETURN)
        
        # 等待查询结果加载
        time.sleep(2)
        
        # 6. 获取终端名称
        try:
            terminal_name_element = wait.until(EC.presence_of_element_located((By.XPATH, TERMINAL_NAME_XPATH)))
            terminal_name = terminal_name_element.text.strip()
            logger.info("[旧准入系统] 获取到终端名称: {name}", name=terminal_name)
        except Exception as e:
            logger.error("[旧准入系统] 未找到终端名称: {e}", e=e)
            return {"status": 1, "result": f"未找到终端名称: {str(e)}"}
        
        # 7. 点击设备管理
        logger.info("[旧准入系统] 点击设备管理")
        device_management_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, DEVICE_MANAGEMENT_LINK_TEXT)))
        device_management_link.click()
        
        # 8. 点击高级检索
        logger.info("[旧准入系统] 点击高级检索")
        advanced_search_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, ADVANCED_SEARCH_LINK_TEXT)))
        advanced_search_link.click()
        
        # 9. 输入终端名称
        logger.info("[旧准入系统] 输入终端名称进行查询: {name}", name=terminal_name)
        term_name_input = wait.until(EC.presence_of_element_located((By.XPATH, TERM_NAME_INPUT_XPATH)))
        term_name_input.clear()
        term_name_input.send_keys(terminal_name)
        
        # 10. 选择"全部"选项
        logger.info("[旧准入系统] 选择全部选项")
        online_select = Select(driver.find_element(By.XPATH, ONLINE_SELECT_XPATH))
        online_select.select_by_visible_text("全部")  # 假设"全部"是选项中的文本
        
        # 11. 点击查找按钮
        logger.info("[旧准入系统] 点击查找按钮")
        search_button = wait.until(EC.element_to_be_clickable((By.XPATH, SEARCH_BUTTON_XPATH)))
        search_button.click()
        
        # 等待搜索结果
        time.sleep(2)
        
        # 12. 点击终端名称链接
        logger.info("[旧准入系统] 点击终端名称链接")
        terminal_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, terminal_name)))
        terminal_link.click()
        
        # 等待详情页面加载
        time.sleep(2)
        
        # 13. 获取MAC地址并处理
        logger.info("[旧准入系统] 获取MAC地址")
        mac_address_element = wait.until(EC.presence_of_element_located((By.XPATH, MAC_ADDRESS_XPATH)))
        mac_address_with_brackets = mac_address_element.text.strip()
        
        # 删除MAC地址中的括号部分
        mac_address = re.sub(r'\([^)]*\)$', '', mac_address_with_brackets).strip()
        logger.info("[旧准入系统] 提取到MAC地址: {mac}", mac=mac_address)
        
        # 获取备注名称（假设也在详情页面中）
        remarks = "无备注"  # 默认值，如果实际页面中有备注，需替换为实际的XPath获取逻辑
        try:
            remarks_element = driver.find_element(By.XPATH, "//td[contains(text(),'备注')]/following-sibling::td[1]")
            remarks = remarks_element.text.strip()
            logger.info("[旧准入系统] 获取到备注名称: {remarks}", remarks=remarks)
        except:
            logger.warning("[旧准入系统] 未找到备注名称，使用默认值")
        
        # 整合结果
        device_info = {
            "device_name": terminal_name,
            "mac_address": mac_address,
            "remarks": remarks
        }
        
        # 返回结果
        logger.info("[旧准入系统] 成功获取设备信息: {info}", info=device_info)
        return {"status": 0, "result": device_info}
        
    except Exception as e:
        logger.error("[旧准入系统] 操作失败: {e}", e=e)
        return {"status": 2, "result": f"旧准入系统操作失败: {str(e)}"}
        
    finally:
        # 确保浏览器关闭
        if driver:
            logger.info("[旧准入系统] 关闭WebDriver")
            driver.quit()



