{"identification": "w5soar", "is_public": true, "name": "消息通知处理", "version": "1.0", "description": "处理剧本执行结果并发送通知", "type": "消息通知", "action": [{"name": "处理消息", "func": "process_message"}], "args": {"process_message": [{"key": "script_type", "type": "select", "required": true, "data": ["s60000_pending", "email", "qianxin_ids", "nsfocus_scan", "s60000_feedback"], "description": "剧本类型"}, {"key": "result_data", "type": "text", "required": true, "default": "@(uuid.result)", "description": "业务APP的JSON返回结果"}, {"key": "execution_id", "type": "text", "required": false, "description": "剧本执行ID（可选）"}, {"key": "token", "type": "text", "required": false, "description": "用户TOKEN（可选，不提供则自动获取系统TOKEN）"}]}}